<?php
/**
 * Optional Enhancements for WooCommerce PDF Invoice Plugin
 * 
 * This script provides optional modifications to improve the user experience
 * by removing legacy update notices and enhancing the plugin interface.
 * 
 * IMPORTANT: These modifications are purely cosmetic and optional.
 * The plugin is already fully functional without these changes.
 */

// Ensure this is run in WordPress context
if (!defined('ABSPATH')) {
    die('This script must be run within WordPress context.');
}

class WC_PDF_Invoice_Enhancements {
    
    public function __construct() {
        add_action('init', array($this, 'init_enhancements'));
    }
    
    /**
     * Initialize enhancements
     */
    public function init_enhancements() {
        // Remove WooThemes Updater notices
        $this->remove_updater_notices();
        
        // Add custom admin notice about plugin status
        add_action('admin_notices', array($this, 'show_plugin_status_notice'));
        
        // Add enhanced plugin information
        add_filter('plugin_row_meta', array($this, 'add_plugin_meta_links'), 10, 2);
    }
    
    /**
     * Remove WooThemes Updater notices
     */
    private function remove_updater_notices() {
        // Remove the admin notice for WooThemes Updater
        remove_action('admin_notices', 'woothemes_updater_notice');
        
        // Remove the plugins_api filter for WooThemes Updater
        remove_filter('plugins_api', 'woothemes_updater_install', 10);
        
        // Prevent the updater notice from showing
        add_filter('pre_option_woothemes_updater_notice_dismissed', '__return_true');
    }
    
    /**
     * Show plugin status notice (dismissible)
     */
    public function show_plugin_status_notice() {
        // Only show on plugin pages or PDF Invoice settings
        $screen = get_current_screen();
        if (!$screen || (!in_array($screen->id, ['plugins', 'woocommerce_page_woocommerce_pdf']) && $screen->parent_base !== 'plugins')) {
            return;
        }
        
        // Check if notice was dismissed
        if (get_user_meta(get_current_user_id(), 'wc_pdf_invoice_status_notice_dismissed', true)) {
            return;
        }
        
        ?>
        <div class="notice notice-success is-dismissible" id="wc-pdf-invoice-status-notice">
            <h3>✅ WooCommerce PDF Invoice Status</h3>
            <p><strong>Good News!</strong> Your WooCommerce PDF Invoice plugin is fully functional and requires no licensing activation.</p>
            <ul style="margin-left: 20px;">
                <li>✅ All features are available immediately</li>
                <li>✅ No license key required</li>
                <li>✅ No server validation needed</li>
                <li>✅ Plugin is permanently activated</li>
            </ul>
            <p><em>You can configure the plugin settings under WooCommerce → PDF Invoice.</em></p>
        </div>
        
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            $(document).on('click', '#wc-pdf-invoice-status-notice .notice-dismiss', function() {
                $.ajax({
                    url: ajaxurl,
                    data: {
                        action: 'dismiss_wc_pdf_invoice_status_notice',
                        nonce: '<?php echo wp_create_nonce('dismiss_notice'); ?>'
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * Add enhanced plugin meta links
     */
    public function add_plugin_meta_links($links, $file) {
        if ($file === 'woocommerce-pdf-invoice/woocommerce-pdf-invoice.php') {
            $links[] = '<span style="color: #4CAF50; font-weight: bold;">✅ Fully Activated</span>';
            $links[] = '<span style="color: #2196F3;">No License Required</span>';
        }
        return $links;
    }
    
    /**
     * Handle AJAX request to dismiss notice
     */
    public function dismiss_status_notice() {
        if (!wp_verify_nonce($_POST['nonce'], 'dismiss_notice')) {
            wp_die('Security check failed');
        }
        
        update_user_meta(get_current_user_id(), 'wc_pdf_invoice_status_notice_dismissed', true);
        wp_die();
    }
}

// AJAX handler for dismissing notice
add_action('wp_ajax_dismiss_wc_pdf_invoice_status_notice', function() {
    if (!wp_verify_nonce($_POST['nonce'], 'dismiss_notice')) {
        wp_die('Security check failed');
    }
    
    update_user_meta(get_current_user_id(), 'wc_pdf_invoice_status_notice_dismissed', true);
    wp_die();
});

/**
 * Plugin Status Dashboard Widget
 */
class WC_PDF_Invoice_Dashboard_Widget {
    
    public function __construct() {
        add_action('wp_dashboard_setup', array($this, 'add_dashboard_widget'));
    }
    
    public function add_dashboard_widget() {
        wp_add_dashboard_widget(
            'wc_pdf_invoice_status',
            '📄 PDF Invoice Status',
            array($this, 'dashboard_widget_content')
        );
    }
    
    public function dashboard_widget_content() {
        $settings = get_option('woocommerce_pdf_invoice_settings');
        $total_invoices = $this->get_total_invoices();
        
        echo '<div style="padding: 10px;">';
        echo '<h4 style="margin-top: 0; color: #4CAF50;">✅ Plugin Status: Fully Operational</h4>';
        
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<tr><td><strong>Plugin Version:</strong></td><td>' . (defined('PDFVERSION') ? PDFVERSION : 'Unknown') . '</td></tr>';
        echo '<tr><td><strong>Total Invoices:</strong></td><td>' . $total_invoices . '</td></tr>';
        echo '<tr><td><strong>License Status:</strong></td><td><span style="color: #4CAF50;">No License Required</span></td></tr>';
        echo '<tr><td><strong>Update Status:</strong></td><td><span style="color: #FF9800;">Legacy System (Inactive)</span></td></tr>';
        echo '</table>';
        
        echo '<div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-left: 4px solid #2196F3;">';
        echo '<strong>💡 Quick Actions:</strong><br>';
        echo '<a href="' . admin_url('admin.php?page=woocommerce_pdf') . '" class="button button-primary" style="margin-right: 10px;">Configure Settings</a>';
        echo '<a href="' . admin_url('edit.php?post_type=shop_order') . '" class="button">View Orders</a>';
        echo '</div>';
        
        echo '</div>';
    }
    
    private function get_total_invoices() {
        global $wpdb;
        
        $count = $wpdb->get_var("
            SELECT COUNT(*) 
            FROM {$wpdb->postmeta} 
            WHERE meta_key = '_invoice_number'
        ");
        
        return $count ? $count : 0;
    }
}

/**
 * Initialize enhancements
 */
function init_wc_pdf_invoice_enhancements() {
    new WC_PDF_Invoice_Enhancements();
    new WC_PDF_Invoice_Dashboard_Widget();
}

// Auto-initialize if this file is included
if (is_admin()) {
    init_wc_pdf_invoice_enhancements();
}

/**
 * Manual activation function
 */
function activate_wc_pdf_invoice_enhancements() {
    init_wc_pdf_invoice_enhancements();
    
    // Add activation notice
    add_action('admin_notices', function() {
        echo '<div class="notice notice-success is-dismissible">';
        echo '<p><strong>WooCommerce PDF Invoice Enhancements Activated!</strong> The plugin interface has been improved.</p>';
        echo '</div>';
    });
}

/**
 * Deactivation function
 */
function deactivate_wc_pdf_invoice_enhancements() {
    // Clean up user meta for dismissed notices
    delete_metadata('user', 0, 'wc_pdf_invoice_status_notice_dismissed', '', true);
    
    // Remove dashboard widget
    remove_action('wp_dashboard_setup', array('WC_PDF_Invoice_Dashboard_Widget', 'add_dashboard_widget'));
}

?>
