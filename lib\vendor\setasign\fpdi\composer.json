{"name": "setasign/fpdi", "homepage": "https://www.setasign.com/fpdi", "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "type": "library", "keywords": ["pdf", "fpdi", "fpdf"], "license": "MIT", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "require": {"php": "^5.6 || ^7.0", "ext-zlib": "*"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured.", "setasign/fpdi-fpdf": "Use this package to automatically evaluate dependencies to FPDF.", "setasign/fpdi-tcpdf": "Use this package to automatically evaluate dependencies to TCPDF.", "setasign/fpdi-tfpdf": "Use this package to automatically evaluate dependencies to tFPDF."}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8", "tecnickcom/tcpdf": "~6.2", "setasign/tfpdf": "1.25"}, "autoload-dev": {"psr-4": {"setasign\\Fpdi\\": "tests/"}}}