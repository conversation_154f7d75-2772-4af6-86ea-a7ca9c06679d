<?php

$cw = array(
	chr(0) => 278, chr(1) => 278, chr(2) => 278, chr(3) => 278, chr(4) => 278, chr(5) => 278, chr(6) => 278, chr(7) => 278, chr(8) => 278, chr(9) => 278, chr(10) => 278, chr(11) => 278, chr(12) => 278, chr(13) => 278, chr(14) => 278, chr(15) => 278, chr(16) => 278, chr(17) => 278, chr(18) => 278, chr(19) => 278, chr(20) => 278, chr(21) => 278,
	chr(22) => 278, chr(23) => 278, chr(24) => 278, chr(25) => 278, chr(26) => 278, chr(27) => 278, chr(28) => 278, chr(29) => 278, chr(30) => 278, chr(31) => 278, ' ' => 278, '!' => 278, '"' => 355, '#' => 556, '$' => 556, '%' => 889, '&' => 667, '\'' => 191, '(' => 333, ')' => 333, '*' => 389, '+' => 584,
	',' => 278, '-' => 333, '.' => 278, '/' => 278, '0' => 556, '1' => 556, '2' => 556, '3' => 556, '4' => 556, '5' => 556, '6' => 556, '7' => 556, '8' => 556, '9' => 556, ':' => 278, ';' => 278, '<' => 584, '=' => 584, '>' => 584, '?' => 556, '@' => 1015, 'A' => 667,
	'B' => 667, 'C' => 722, 'D' => 722, 'E' => 667, 'F' => 611, 'G' => 778, 'H' => 722, 'I' => 278, 'J' => 500, 'K' => 667, 'L' => 556, 'M' => 833, 'N' => 722, 'O' => 778, 'P' => 667, 'Q' => 778, 'R' => 722, 'S' => 667, 'T' => 611, 'U' => 722, 'V' => 667, 'W' => 944,
	'X' => 667, 'Y' => 667, 'Z' => 611, '[' => 278, '\\' => 278, ']' => 278, '^' => 469, '_' => 556, '`' => 333, 'a' => 556, 'b' => 556, 'c' => 500, 'd' => 556, 'e' => 556, 'f' => 278, 'g' => 556, 'h' => 556, 'i' => 222, 'j' => 222, 'k' => 500, 'l' => 222, 'm' => 833,
	'n' => 556, 'o' => 556, 'p' => 556, 'q' => 556, 'r' => 333, 's' => 500, 't' => 278, 'u' => 556, 'v' => 500, 'w' => 722, 'x' => 500, 'y' => 500, 'z' => 500, '{' => 334, '|' => 260, '}' => 334, '~' => 584, chr(127) => 350, chr(128) => 556, chr(129) => 350, chr(130) => 222, chr(131) => 556,
	chr(132) => 333, chr(133) => 1000, chr(134) => 556, chr(135) => 556, chr(136) => 333, chr(137) => 1000, chr(138) => 667, chr(139) => 333, chr(140) => 1000, chr(141) => 350, chr(142) => 611, chr(143) => 350, chr(144) => 350, chr(145) => 222, chr(146) => 222, chr(147) => 333, chr(148) => 333, chr(149) => 350, chr(150) => 556, chr(151) => 1000, chr(152) => 333, chr(153) => 1000,
	chr(154) => 500, chr(155) => 333, chr(156) => 944, chr(157) => 350, chr(158) => 500, chr(159) => 667, chr(160) => 278, chr(161) => 333, chr(162) => 556, chr(163) => 556, chr(164) => 556, chr(165) => 556, chr(166) => 260, chr(167) => 556, chr(168) => 333, chr(169) => 737, chr(170) => 370, chr(171) => 556, chr(172) => 584, chr(173) => 333, chr(174) => 737, chr(175) => 333,
	chr(176) => 400, chr(177) => 584, chr(178) => 333, chr(179) => 333, chr(180) => 333, chr(181) => 556, chr(182) => 537, chr(183) => 278, chr(184) => 333, chr(185) => 333, chr(186) => 365, chr(187) => 556, chr(188) => 834, chr(189) => 834, chr(190) => 834, chr(191) => 611, chr(192) => 667, chr(193) => 667, chr(194) => 667, chr(195) => 667, chr(196) => 667, chr(197) => 667,
	chr(198) => 1000, chr(199) => 722, chr(200) => 667, chr(201) => 667, chr(202) => 667, chr(203) => 667, chr(204) => 278, chr(205) => 278, chr(206) => 278, chr(207) => 278, chr(208) => 722, chr(209) => 722, chr(210) => 778, chr(211) => 778, chr(212) => 778, chr(213) => 778, chr(214) => 778, chr(215) => 584, chr(216) => 778, chr(217) => 722, chr(218) => 722, chr(219) => 722,
	chr(220) => 722, chr(221) => 667, chr(222) => 667, chr(223) => 611, chr(224) => 556, chr(225) => 556, chr(226) => 556, chr(227) => 556, chr(228) => 556, chr(229) => 556, chr(230) => 889, chr(231) => 500, chr(232) => 556, chr(233) => 556, chr(234) => 556, chr(235) => 556, chr(236) => 278, chr(237) => 278, chr(238) => 278, chr(239) => 278, chr(240) => 556, chr(241) => 556,
	chr(242) => 556, chr(243) => 556, chr(244) => 556, chr(245) => 556, chr(246) => 556, chr(247) => 584, chr(248) => 611, chr(249) => 556, chr(250) => 556, chr(251) => 556, chr(252) => 556, chr(253) => 500, chr(254) => 556, chr(255) => 500);

//$desc=array('Ascent'=>718,'Descent'=>-207,'CapHeight'=>718,'FontBBox'=>'[-170 -225 1116 931]');
$desc = array('Flags' => 96, 'FontBBox' => '[-170 -225 1116 931]', 'ItalicAngle' => -12, 'Ascent' => 931, 'Descent' => -225, 'Leading' => 0, 'CapHeight' => 718, 'XHeight' => 523, 'StemV' => 88, 'StemH' => 76, 'AvgWidth' => 513, 'MaxWidth' => 1015, 'MissingWidth' => 513);
$up = -100;
$ut = 50;
$kerninfo = array(chr(49) => array(chr(49) => -74,), chr(65) => array(chr(84) => -74, chr(86) => -55, chr(87) => -18, chr(89) => -74, chr(118) => -18, chr(119) => -18, chr(121) => -8, chr(146) => -37,), chr(70) => array(chr(44) => -128, chr(46) => -128, chr(65) => -74,), chr(76) => array(chr(84) => -74, chr(86) => -55, chr(87) => -37, chr(89) => -91, chr(121) => -18, chr(146) => -55,), chr(80) => array(chr(44) => -128, chr(46) => -128, chr(65) => -74,), chr(82) => array(chr(84) => -18, chr(86) => -18, chr(87) => -18, chr(89) => -37,), chr(84) => array(chr(44) => -91, chr(46) => -91, chr(58) => -74, chr(65) => -74, chr(79) => -18, chr(97) => -91, chr(99) => -91, chr(101) => -91, chr(105) => -8, chr(111) => -91, chr(114) => -74, chr(115) => -91, chr(117) => -74, chr(119) => -74, chr(121) => -74,), chr(86) => array(chr(44) => -74, chr(46) => -74, chr(58) => -18, chr(65) => -55, chr(97) => -37, chr(101) => -37, chr(105) => -18, chr(111) => -37, chr(114) => -18, chr(117) => -18, chr(121) => -18,), chr(87) => array(chr(44) => -37, chr(46) => -37, chr(65) => -18, chr(97) => -18, chr(101) => -18, chr(105) => -8,), chr(89) => array(chr(44) => -91, chr(46) => -91, chr(58) => -37, chr(65) => -55, chr(97) => -74, chr(101) => -55, chr(105) => -18, chr(111) => -55, chr(112) => -55, chr(113) => -55, chr(117) => -37, chr(118) => -37,), chr(102) => array(chr(146) => 37,), chr(114) => array(chr(44) => -55, chr(46) => -37, chr(146) => 37,), chr(118) => array(chr(44) => -74, chr(46) => -74,), chr(119) => array(chr(44) => -55, chr(46) => -55,), chr(121) => array(chr(44) => -74, chr(46) => -74,), chr(145) => array(chr(145) => -37,), chr(146) => array(chr(115) => -18, chr(146) => -37,),);
