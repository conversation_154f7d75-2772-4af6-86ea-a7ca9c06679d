<?php
$distFontDir = $rootDir . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR . 'fonts' . DIRECTORY_SEPARATOR;
return array(
    'sans-serif' =>
        array(
            'normal' => $distFontDir . 'Helvetica',
            'bold' => $distFontDir . 'Helvetica-Bold',
            'italic' => $distFontDir . 'Helvetica-Oblique',
            'bold_italic' => $distFontDir . 'Helvetica-BoldOblique'
        ),
    'times' =>
        array(
            'normal' => $distFontDir . 'Times-Roman',
            'bold' => $distFontDir . 'Times-Bold',
            'italic' => $distFontDir . 'Times-Italic',
            'bold_italic' => $distFontDir . 'Times-BoldItalic'
        ),
    'times-roman' =>
        array(
            'normal' => $distFontDir . 'Times-Roman',
            'bold' => $distFontDir . 'Times-Bold',
            'italic' => $distFontDir . 'Times-Italic',
            'bold_italic' => $distFontDir . 'Times-BoldItalic'
        ),
    'courier' =>
        array(
            'normal' => $distFontDir . 'Courier',
            'bold' => $distFontDir . 'Courier-Bold',
            'italic' => $distFontDir . 'Courier-Oblique',
            'bold_italic' => $distFontDir . 'Courier-BoldOblique'
        ),
    'helvetica' =>
        array(
            'normal' => $distFontDir . 'Helvetica',
            'bold' => $distFontDir . 'Helvetica-Bold',
            'italic' => $distFontDir . 'Helvetica-Oblique',
            'bold_italic' => $distFontDir . 'Helvetica-BoldOblique'
        ),
    'zapfdingbats' =>
        array(
            'normal' => $distFontDir . 'ZapfDingbats',
            'bold' => $distFontDir . 'ZapfDingbats',
            'italic' => $distFontDir . 'ZapfDingbats',
            'bold_italic' => $distFontDir . 'ZapfDingbats'
        ),
    'symbol' =>
        array(
            'normal' => $distFontDir . 'Symbol',
            'bold' => $distFontDir . 'Symbol',
            'italic' => $distFontDir . 'Symbol',
            'bold_italic' => $distFontDir . 'Symbol'
        ),
    'serif' =>
        array(
            'normal' => $distFontDir . 'Times-Roman',
            'bold' => $distFontDir . 'Times-Bold',
            'italic' => $distFontDir . 'Times-Italic',
            'bold_italic' => $distFontDir . 'Times-BoldItalic'
        ),
    'monospace' =>
        array(
            'normal' => $distFontDir . 'Courier',
            'bold' => $distFontDir . 'Courier-Bold',
            'italic' => $distFontDir . 'Courier-Oblique',
            'bold_italic' => $distFontDir . 'Courier-BoldOblique'
        ),
    'fixed' =>
        array(
            'normal' => $distFontDir . 'Courier',
            'bold' => $distFontDir . 'Courier-Bold',
            'italic' => $distFontDir . 'Courier-Oblique',
            'bold_italic' => $distFontDir . 'Courier-BoldOblique'
        ),
    'dejavu sans' =>
        array(
            'bold' => $distFontDir . 'DejaVuSans-Bold',
            'bold_italic' => $distFontDir . 'DejaVuSans-BoldOblique',
            'italic' => $distFontDir . 'DejaVuSans-Oblique',
            'normal' => $distFontDir . 'DejaVuSans'
        ),
    'dejavu sans mono' =>
        array(
            'bold' => $distFontDir . 'DejaVuSansMono-Bold',
            'bold_italic' => $distFontDir . 'DejaVuSansMono-BoldOblique',
            'italic' => $distFontDir . 'DejaVuSansMono-Oblique',
            'normal' => $distFontDir . 'DejaVuSansMono'
        ),
    'dejavu serif' =>
        array(
            'bold' => $distFontDir . 'DejaVuSerif-Bold',
            'bold_italic' => $distFontDir . 'DejaVuSerif-BoldItalic',
            'italic' => $distFontDir . 'DejaVuSerif-Italic',
            'normal' => $distFontDir . 'DejaVuSerif'
        )
);