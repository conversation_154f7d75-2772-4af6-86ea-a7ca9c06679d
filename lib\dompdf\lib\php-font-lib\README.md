# PHP Font Lib

[![Build Status](https://travis-ci.org/PhenX/php-font-lib.svg?branch=master)](https://travis-ci.org/PhenX/php-font-lib)


This library can be used to:
 * Read TrueType, OpenType (with TrueType glyphs), WOFF font files
 * Extract basic info (name, style, etc)
 * Extract advanced info (horizontal metrics, glyph names, glyph shapes, etc)
 * Make an Adobe Font Metrics (AFM) file from a font

You can find a demo GUI [here](http://pxd.me/php-font-lib/www/font_explorer.html).

This project was initiated by the need to read font files in the [DOMPDF project](https://github.com/dompdf/dompdf).
