/**
 *	WooCommerce PDF Admin CSS
 */
 
.widefat .column-pdf_invoice_num {
    text-align: center;
    width: 100px;
}

table.pdfsetup {
	margin-top:20px !important;
}

table.pdfsetup .pdfheaderrow {
	background: #666;
	color: #fff;
	font-weight:bold;
}

.pdfsetup th {
    font-weight: bold;
    text-shadow: none;
	color:#FFF;
}

.pdfsetup th.title {
	color:#000;
}

.pdf-even {
	background: #CCC;
}

.pdf-odd {
	
}

.pdfsetup .warning, .pdfsetup .failed {
	color:red;
	font-weight: bold;
}

.pdfsetup .ok {
	color:green;
	font-weight: bold;
}

h3.dompdf-config {
	margin:20px 0px;
	border-bottom:1px dotted #666;
}

.dompgf-debugging-table th {
	text-align: left;
}

.dompgf-debugging-table input[type=submit] {
	text-align: left !important;
	/* padding: 20px 5px !important; */
	font-size:20pt;
}

/* WC 2.1 Icons */
@font-face {
  font-family: "untitled-font-1";
  src:url("../fonts/untitled-font-1.eot");
  src:url("../fonts/untitled-font-1.eot?#iefix") format("embedded-opentype"),
    url("../fonts/untitled-font-1.woff") format("woff"),
    url("../fonts/untitled-font-1.ttf") format("truetype"),
    url("../fonts/untitled-font-1.svg#untitled-font-1") format("svg");
  font-weight: normal;
  font-style: normal;

}

.order_actions .icon-sendpdf, .order_actions .icon-downloadpdf {
	display:block;
	text-indent:-9999px;
	position:relative;
	height:1em;
	width:1em;
	padding:0!important;
	height:2em!important;
	width:2em
}

[data-icon]::after {
  font-family: "untitled-font-1" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

[class^="icon-"]::after,
[class*=" icon-"]::after {
  font-family: "untitled-font-1" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.order_actions a.icon-sendpdf::after,.order_actions a.icon-downloadpdf::after{
	font-family: "untitled-font-1";
	speak:none;
	font-weight:400;
	font-variant:normal;
	text-transform:none;
	line-height:1;
	-webkit-font-smoothing:antialiased;
	margin:0;
	text-indent:0;
	position:absolute;
	top:0;
	left:0;
	width:100%;
	height:100%;
	text-align:center;
	content:"\a001";
	line-height:1.85
}

.widefat .column-wc_actions a.icon-sendpdf::after {
  content: "\a001" !important;
}

.widefat .column-wc_actions a.icon-downloadpdf::after {
  content: "\b001" !important;
}

.invoice_meta_group span {
	float:left;
	width:30%;
}

.invoice_meta_group ul li input {
	float:left;
	width:50%;
	border: 1px solid #ddd;
	box-shadow: inset 0 1px 2px rgba( 0, 0, 0, 0.07 );
	background-color: #fff;
	color: #32373c;
	outline: none;
	transition: 0.05s border-color ease-in-out;
}

.invoice_meta_group ul li {
	clear:both;
	padding:10px 0;
}

.invoice_meta_group p {
	clear:both;
	font-weight: bold;
	font-size: 120%;
	padding: 20px 0;
}

.invoice_details_group li .pdf_invoice_metabox_download_invoice {
	float: left;
}

.invoice_details_group li .pdf_invoice_metabox_send_invoice {
	float: right;
}

/* Help page */
.pdf-invoice-template-table .template_even {
	background-color: #cacaca;
}

