<?php
/**
 * Quick Status Check for WooCommerce PDF Invoice Plugin
 * 
 * A simple script to quickly verify plugin status and functionality
 * Can be run via browser, WP-CLI, or included in other scripts
 */

// Prevent direct access outside WordPress
if (!defined('ABSPATH')) {
    // If running outside WordPress, try to load WordPress
    $wp_load_paths = [
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php'
    ];
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once($path);
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die("WordPress not found. Please run this script from within WordPress or place it in the WordPress root directory.\n");
    }
}

/**
 * Quick status check function
 */
function wc_pdf_invoice_quick_check() {
    $results = [];
    $is_cli = defined('WP_CLI') && WP_CLI;
    
    // Output helper
    $output = function($message, $is_error = false) use ($is_cli) {
        if ($is_cli) {
            WP_CLI::line($message);
        } else {
            $color = $is_error ? '#f44336' : '#333';
            echo "<div style='color: $color; margin: 5px 0;'>$message</div>\n";
        }
    };
    
    if (!$is_cli) {
        echo "<h2>🔍 WooCommerce PDF Invoice - Quick Status Check</h2>\n";
        echo "<div style='font-family: monospace; background: #f9f9f9; padding: 15px; border-radius: 5px;'>\n";
    }
    
    // 1. Check if plugin exists and is active
    $plugin_file = 'woocommerce-pdf-invoice/woocommerce-pdf-invoice.php';
    $plugin_exists = file_exists(WP_PLUGIN_DIR . '/' . $plugin_file);
    $plugin_active = is_plugin_active($plugin_file);
    
    $output("1. Plugin File: " . ($plugin_exists ? "✅ Found" : "❌ Missing"));
    $output("2. Plugin Active: " . ($plugin_active ? "✅ Yes" : "❌ No"));
    
    $results['plugin_status'] = $plugin_exists && $plugin_active;
    
    // 2. Check version
    if (defined('PDFVERSION')) {
        $output("3. Plugin Version: ✅ " . PDFVERSION);
        $results['version'] = PDFVERSION;
    } else {
        $output("3. Plugin Version: ❌ Not defined");
        $results['version'] = false;
    }
    
    // 3. Check main classes
    $classes = ['WC_pdf_admin', 'WC_pdf_functions', 'WC_send_pdf'];
    $classes_loaded = 0;
    
    foreach ($classes as $class) {
        if (class_exists($class)) {
            $classes_loaded++;
        }
    }
    
    $output("4. Core Classes: " . ($classes_loaded === count($classes) ? "✅ All loaded ($classes_loaded/" . count($classes) . ")" : "⚠️ Partial ($classes_loaded/" . count($classes) . ")"));
    $results['classes'] = $classes_loaded === count($classes);
    
    // 4. Check settings
    $settings = get_option('woocommerce_pdf_invoice_settings');
    $output("5. Plugin Settings: " . ($settings ? "✅ Found" : "❌ Missing"));
    $results['settings'] = !empty($settings);
    
    // 5. Check for licensing system (should be none)
    $licensing_functions = ['license_check', 'validate_license', 'check_activation'];
    $licensing_found = false;
    
    foreach ($licensing_functions as $func) {
        if (function_exists($func)) {
            $licensing_found = true;
            break;
        }
    }
    
    $output("6. Licensing System: " . ($licensing_found ? "⚠️ Found (unexpected)" : "✅ None (correct)"));
    $results['no_licensing'] = !$licensing_found;
    
    // 6. Check WooCommerce dependency
    $wc_active = class_exists('WooCommerce') || function_exists('WC');
    $output("7. WooCommerce: " . ($wc_active ? "✅ Active" : "❌ Required"));
    $results['woocommerce'] = $wc_active;
    
    // 7. Check file permissions
    $upload_dir = wp_upload_dir();
    $pdf_dir = $upload_dir['basedir'] . '/woocommerce_pdf_invoice';
    $pdf_dir_writable = is_writable($pdf_dir) || wp_mkdir_p($pdf_dir);
    
    $output("8. Upload Directory: " . ($pdf_dir_writable ? "✅ Writable" : "⚠️ Check permissions"));
    $results['permissions'] = $pdf_dir_writable;
    
    // 8. Overall status
    $all_good = $results['plugin_status'] && 
                $results['classes'] && 
                $results['no_licensing'] && 
                $results['woocommerce'];
    
    if (!$is_cli) {
        echo "</div>\n";
        echo "<div style='margin: 20px 0; padding: 15px; border-radius: 5px; border: 2px solid " . ($all_good ? "#4CAF50" : "#f44336") . ";'>\n";
    }
    
    if ($all_good) {
        $output("🎉 OVERALL STATUS: ✅ FULLY FUNCTIONAL");
        $output("✅ No licensing bypass needed - plugin is ready to use!");
        $output("💡 Configure settings at: WooCommerce → PDF Invoice");
    } else {
        $output("⚠️ OVERALL STATUS: Issues detected");
        $output("❌ Please resolve the issues above");
    }
    
    if (!$is_cli) {
        echo "</div>\n";
        
        // Add quick action buttons
        if ($all_good) {
            echo "<div style='margin: 20px 0;'>\n";
            echo "<h3>Quick Actions:</h3>\n";
            echo "<a href='" . admin_url('admin.php?page=woocommerce_pdf') . "' class='button button-primary'>Configure Plugin Settings</a> ";
            echo "<a href='" . admin_url('edit.php?post_type=shop_order') . "' class='button'>View Orders</a> ";
            echo "<a href='" . admin_url('plugins.php') . "' class='button'>Manage Plugins</a>\n";
            echo "</div>\n";
        }
    }
    
    return $results;
}

/**
 * WP-CLI command registration
 */
if (defined('WP_CLI') && WP_CLI) {
    WP_CLI::add_command('pdf-invoice check', function() {
        WP_CLI::line("Running WooCommerce PDF Invoice status check...\n");
        $results = wc_pdf_invoice_quick_check();
        
        if ($results['plugin_status'] && $results['no_licensing']) {
            WP_CLI::success("Plugin is fully functional and requires no licensing bypass!");
        } else {
            WP_CLI::error("Issues detected. Please review the output above.");
        }
    });
}

/**
 * Web interface
 */
if (!defined('WP_CLI') && (is_admin() || isset($_GET['pdf_check']))) {
    // Add admin menu item for quick check
    add_action('admin_menu', function() {
        add_submenu_page(
            'tools.php',
            'PDF Invoice Status',
            'PDF Invoice Status',
            'manage_options',
            'pdf-invoice-status',
            'wc_pdf_invoice_quick_check'
        );
    });
    
    // Handle direct access via URL parameter
    if (isset($_GET['pdf_check']) && current_user_can('manage_options')) {
        add_action('init', function() {
            wc_pdf_invoice_quick_check();
            exit;
        });
    }
}

/**
 * Shortcode for displaying status
 */
add_shortcode('pdf_invoice_status', function() {
    if (!current_user_can('manage_options')) {
        return '<p>Access denied.</p>';
    }
    
    ob_start();
    wc_pdf_invoice_quick_check();
    return ob_get_clean();
});

/**
 * Function for programmatic use
 */
function get_wc_pdf_invoice_status() {
    ob_start();
    $results = wc_pdf_invoice_quick_check();
    ob_end_clean();
    return $results;
}

// Auto-run if accessed directly via browser
if (!defined('WP_CLI') && !is_admin() && basename($_SERVER['PHP_SELF']) === 'quick_status_check.php') {
    if (current_user_can('manage_options')) {
        wc_pdf_invoice_quick_check();
    } else {
        echo "<h2>Access Denied</h2><p>You need administrator privileges to run this check.</p>";
    }
}

?>
