{"name": "dompdf/dompdf", "type": "library", "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "license": "LGPL-2.1", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "autoload-dev": {"psr-4": {"Dompdf\\Tests\\": "tests/"}}, "require": {"php": "^7.1 || ^8.0", "ext-dom": "*", "ext-mbstring": "*", "phenx/php-font-lib": "^0.5.2", "phenx/php-svg-lib": "^0.3.3"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8 || ^9", "squizlabs/php_codesniffer": "^3.5", "mockery/mockery": "^1.3"}, "suggest": {"ext-gd": "Needed to process images", "ext-imagick": "Improves image processing performance", "ext-gmagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "extra": {"branch-alias": {"dev-develop": "0.7-dev"}}}