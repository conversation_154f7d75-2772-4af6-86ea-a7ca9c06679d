[{"name": "mpdf/mpdf", "version": "v8.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "c13ebc0fd5cc0613dfb1fd37d55a67859b92cf0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/c13ebc0fd5cc0613dfb1fd37d55a67859b92cf0c", "reference": "c13ebc0fd5cc0613dfb1fd37d55a67859b92cf0c", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|9.99.99", "php": "^5.6 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0", "psr/log": "^1.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^0.9.5", "mpdf/qrcode": "^1.0.0", "phpunit/phpunit": "^5.0", "squizlabs/php_codesniffer": "^2.7.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "time": "2019-03-15T22:03:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-development": "7.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"]}, {"name": "myclabs/deep-copy", "version": "1.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "3e01bdad3e18354c3dce54466b7fbe33a9f9f7f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3e01bdad3e18354c3dce54466b7fbe33a9f9f7f8", "reference": "3e01bdad3e18354c3dce54466b7fbe33a9f9f7f8", "shasum": ""}, "require": {"php": "^7.1"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "time": "2018-06-11T23:09:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"]}, {"name": "paragonie/random_compat", "version": "v9.99.99", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": ""}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "time": "2018-07-02T15:55:56+00:00", "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"]}, {"name": "psr/log", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd", "reference": "6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2018-11-20T15:27:04+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"]}, {"name": "setasign/fpdi", "version": "v2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "3c266002f8044f61b17329f7cd702d44d73f0f7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/3c266002f8044f61b17329f7cd702d44d73f0f7f", "reference": "3c266002f8044f61b17329f7cd702d44d73f0f7f", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8", "setasign/tfpdf": "1.25", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured.", "setasign/fpdi-fpdf": "Use this package to automatically evaluate dependencies to FPDF.", "setasign/fpdi-tcpdf": "Use this package to automatically evaluate dependencies to TCPDF.", "setasign/fpdi-tfpdf": "Use this package to automatically evaluate dependencies to tFPDF."}, "time": "2019-01-30T14:11:19+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"]}]