<?php
/**
 * WooCommerce PDF Invoice Plugin Verification Script
 * 
 * This script verifies the plugin's functionality and licensing status
 * Run this in WordPress admin or via WP-CLI
 */

// Ensure this is run in WordPress context
if (!defined('ABSPATH')) {
    die('This script must be run within WordPress context.');
}

class WC_PDF_Invoice_Verifier {
    
    private $results = [];
    
    public function __construct() {
        $this->run_verification();
    }
    
    /**
     * Run all verification checks
     */
    public function run_verification() {
        echo "<h2>🔍 WooCommerce PDF Invoice Plugin Verification</h2>\n";
        
        $this->check_plugin_status();
        $this->check_licensing_system();
        $this->check_functionality();
        $this->check_database_options();
        $this->check_update_system();
        $this->display_summary();
    }
    
    /**
     * Check if plugin is active and basic info
     */
    private function check_plugin_status() {
        echo "<h3>📋 Plugin Status</h3>\n";
        
        $plugin_file = 'woocommerce-pdf-invoice/woocommerce-pdf-invoice.php';
        $is_active = is_plugin_active($plugin_file);
        
        $this->results['plugin_active'] = $is_active;
        
        echo "<ul>\n";
        echo "<li><strong>Plugin Active:</strong> " . ($is_active ? "✅ YES" : "❌ NO") . "</li>\n";
        
        if (defined('PDFVERSION')) {
            echo "<li><strong>Version:</strong> " . PDFVERSION . "</li>\n";
            $this->results['version'] = PDFVERSION;
        }
        
        if (class_exists('WC_pdf_admin')) {
            echo "<li><strong>Main Class Loaded:</strong> ✅ YES</li>\n";
            $this->results['main_class'] = true;
        } else {
            echo "<li><strong>Main Class Loaded:</strong> ❌ NO</li>\n";
            $this->results['main_class'] = false;
        }
        
        echo "</ul>\n";
    }
    
    /**
     * Check for any licensing system
     */
    private function check_licensing_system() {
        echo "<h3>🔐 Licensing System Analysis</h3>\n";
        
        $licensing_functions = [
            'license_check',
            'validate_license',
            'check_activation',
            'license_validation',
            'activation_check'
        ];
        
        $found_licensing = false;
        
        echo "<ul>\n";
        foreach ($licensing_functions as $func) {
            if (function_exists($func)) {
                echo "<li><strong>$func:</strong> ⚠️ FOUND</li>\n";
                $found_licensing = true;
            }
        }
        
        if (!$found_licensing) {
            echo "<li><strong>Licensing Functions:</strong> ✅ NONE FOUND (Good!)</li>\n";
        }
        
        // Check for license-related database options
        $license_options = [
            'woocommerce_pdf_invoice_license',
            'woocommerce_pdf_invoice_activation',
            'woocommerce_pdf_invoice_license_key',
            'pdf_invoice_license_status'
        ];
        
        $found_license_options = false;
        foreach ($license_options as $option) {
            $value = get_option($option);
            if ($value !== false) {
                echo "<li><strong>License Option '$option':</strong> ⚠️ FOUND</li>\n";
                $found_license_options = true;
            }
        }
        
        if (!$found_license_options) {
            echo "<li><strong>License Database Options:</strong> ✅ NONE FOUND (Good!)</li>\n";
        }
        
        $this->results['licensing_system'] = $found_licensing || $found_license_options;
        
        echo "</ul>\n";
    }
    
    /**
     * Check plugin functionality
     */
    private function check_functionality() {
        echo "<h3>⚙️ Functionality Check</h3>\n";
        
        echo "<ul>\n";
        
        // Check if settings page exists
        if (function_exists('add_submenu_page')) {
            echo "<li><strong>Settings Page:</strong> ✅ Available</li>\n";
            $this->results['settings_page'] = true;
        }
        
        // Check if main classes exist
        $required_classes = [
            'WC_pdf_functions',
            'WC_send_pdf',
            'WC_pdf_admin_settings'
        ];
        
        foreach ($required_classes as $class) {
            if (class_exists($class)) {
                echo "<li><strong>$class:</strong> ✅ Loaded</li>\n";
                $this->results['classes'][$class] = true;
            } else {
                echo "<li><strong>$class:</strong> ❌ Missing</li>\n";
                $this->results['classes'][$class] = false;
            }
        }
        
        echo "</ul>\n";
    }
    
    /**
     * Check database options
     */
    private function check_database_options() {
        echo "<h3>🗄️ Database Options</h3>\n";
        
        $settings = get_option('woocommerce_pdf_invoice_settings');
        $version = get_option('woocommerce_pdf_invoice_version');
        
        echo "<ul>\n";
        echo "<li><strong>Main Settings:</strong> " . ($settings ? "✅ Found" : "❌ Missing") . "</li>\n";
        echo "<li><strong>Version Option:</strong> " . ($version ? "✅ Found ($version)" : "❌ Missing") . "</li>\n";
        
        if ($settings && is_array($settings)) {
            echo "<li><strong>Settings Count:</strong> " . count($settings) . " options</li>\n";
        }
        
        $this->results['database_options'] = [
            'settings' => !empty($settings),
            'version' => !empty($version)
        ];
        
        echo "</ul>\n";
    }
    
    /**
     * Check update system
     */
    private function check_update_system() {
        echo "<h3>🔄 Update System</h3>\n";
        
        global $woothemes_queued_updates;
        
        echo "<ul>\n";
        
        if (function_exists('woothemes_queue_update')) {
            echo "<li><strong>WooThemes Update Function:</strong> ✅ Available</li>\n";
        }
        
        if (isset($woothemes_queued_updates) && !empty($woothemes_queued_updates)) {
            echo "<li><strong>Queued Updates:</strong> " . count($woothemes_queued_updates) . "</li>\n";
            foreach ($woothemes_queued_updates as $update) {
                if (isset($update->product_id) && $update->product_id == '228318') {
                    echo "<li><strong>This Plugin Queued:</strong> ✅ YES (Product ID: {$update->product_id})</li>\n";
                }
            }
        }
        
        if (class_exists('WooThemes_Updater')) {
            echo "<li><strong>WooThemes Updater:</strong> ⚠️ Installed</li>\n";
        } else {
            echo "<li><strong>WooThemes Updater:</strong> ✅ Not Installed (Normal)</li>\n";
        }
        
        echo "</ul>\n";
    }
    
    /**
     * Display verification summary
     */
    private function display_summary() {
        echo "<h3>📊 Verification Summary</h3>\n";
        
        $is_functional = $this->results['plugin_active'] && 
                        $this->results['main_class'] && 
                        !$this->results['licensing_system'];
        
        echo "<div style='padding: 15px; border: 2px solid " . ($is_functional ? "#4CAF50" : "#f44336") . "; border-radius: 5px; margin: 10px 0;'>\n";
        
        if ($is_functional) {
            echo "<h4 style='color: #4CAF50;'>✅ PLUGIN IS FULLY FUNCTIONAL</h4>\n";
            echo "<p><strong>Result:</strong> No licensing bypass needed. The plugin is already permanently activated and fully operational.</p>\n";
            echo "<p><strong>Action Required:</strong> None. You can use all plugin features immediately.</p>\n";
        } else {
            echo "<h4 style='color: #f44336;'>❌ ISSUES DETECTED</h4>\n";
            echo "<p><strong>Result:</strong> Some issues were found that may affect functionality.</p>\n";
            echo "<p><strong>Action Required:</strong> Review the issues above and resolve them.</p>\n";
        }
        
        echo "</div>\n";
        
        // Additional recommendations
        echo "<h4>💡 Recommendations:</h4>\n";
        echo "<ul>\n";
        echo "<li>Configure plugin settings in WooCommerce → PDF Invoice</li>\n";
        echo "<li>Test PDF generation with a sample order</li>\n";
        echo "<li>Optionally remove WooThemes Updater notices (cosmetic only)</li>\n";
        echo "<li>Backup your site before making any modifications</li>\n";
        echo "</ul>\n";
    }
}

// Run verification if accessed directly
if (is_admin() || (defined('WP_CLI') && WP_CLI)) {
    new WC_PDF_Invoice_Verifier();
}

/**
 * Function to run verification programmatically
 */
function verify_wc_pdf_invoice_plugin() {
    return new WC_PDF_Invoice_Verifier();
}
?>
